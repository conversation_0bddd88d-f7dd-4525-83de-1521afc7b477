<template>
    <div class="loading-state">
        <div class="loading-card">
            <i class="pi pi-spin pi-spinner"></i>
            <span class="loading-text">{{ text }}</span>
        </div>
    </div>
</template>

<script setup>
defineProps({
    text: {
        type: String,
        default: '加载中...'
    }
});
</script>

<style lang="scss" scoped>
.loading-state {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    // background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(4px);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
}

.loading-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 24px 36px;

    .pi {
        font-size: 24px;
        color: #1677ff;
    }

    .loading-text {
        font-size: 14px;
        color: #1677ff;
        font-weight: 500;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
