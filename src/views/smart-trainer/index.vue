<template>
    <div class="smart-trainer-layout">
        <!-- 主内容区域 -->
        <div class="main-content">
            <RouterView />
        </div>

        <!-- 固定底部导航栏 -->
        <BottomNavigation />
    </div>
</template>

<script setup>
import { onMounted } from 'vue';
import BottomNavigation from './components/BottomNavigation.vue';

onMounted(() => {
    console.log('智能训练器主布局已加载');
});
</script>

<style lang="scss" scoped>
.smart-trainer-layout {
    display: flex;
    flex-direction: column;
    height: 100vh;

    .main-content {
        flex: 1;
        overflow-y: auto;
        padding-bottom: 60px; // 为底部导航栏留出空间

        // 确保内容区域可以滚动
        -webkit-overflow-scrolling: touch;

        // 隐藏滚动条但保持滚动功能
        scrollbar-width: none; // Firefox
        -ms-overflow-style: none; // IE

        &::-webkit-scrollbar {
            display: none !important; // Chrome, Safari, Edge
        }
    }
}
</style>
