<template>
    <div class="recommend-courses">
        <div class="section-header">
            <h3 class="section-title">推荐课程</h3>
            <div class="more-link" @click="handleViewMore">
                更多
                <i class="pi pi-angle-right"></i>
            </div>
        </div>

        <div class="courses-list">
            <div
                v-for="course in courses"
                :key="course.id"
                class="course-card"
                @click="handleCourseClick(course)"
            >
                <div class="course-cover">
                    <img :src="course.cover" :alt="course.name" class="cover-image" />
                </div>

                <div class="course-info">
                    <div class="course-main">
                        <h4 class="course-name">{{ course.name }}</h4>
                        <div class="course-source">
                            <i class="pi pi-building"></i>
                            <span>{{ course.source }}</span>
                        </div>
                    </div>
                    <div class="course-footer">
                        <div class="course-type-badge" :class="course.type">
                            {{ course.typeText }}
                        </div>
                        <div class="course-meta">
                            <i class="pi pi-file" v-if="course.format === 'document'"></i>
                            <i class="pi pi-play" v-else></i>
                            <span>{{ course.formatText }} · {{ course.metaInfo }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 定义事件
const emit = defineEmits(['viewMore', 'courseClick']);

// Mock 课程数据
const courses = ref([
    {
        id: 1,
        name: '汽车之家员工手册',
        source: '人力资源部',
        format: 'document', // document | video
        formatText: '课件',
        metaInfo: '45页',
        type: 'required', // required | optional
        typeText: '必修',
        cover: 'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/swiper.png'
    },
    {
        id: 2,
        name: '新员工入职指南',
        source: '人力资源部',
        format: 'video',
        formatText: '视频',
        metaInfo: '30分钟',
        type: 'required',
        typeText: '必修',
        cover: 'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/swiper.png'
    },
    {
        id: 3,
        name: '企业文化培训',
        source: '企业文化部',
        format: 'document',
        formatText: '课件',
        metaInfo: '28页',
        type: 'optional',
        typeText: '选修',
        cover: 'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/swiper.png'
    }
]);

/**
 * 处理课程点击事件
 * @param {Object} course - 课程对象
 */
const handleCourseClick = course => {
    console.log('点击课程:', course.name);
    emit('courseClick', course);
};

/**
 * 处理查看更多点击事件
 */
const handleViewMore = () => {
    console.log('查看更多课程');
    emit('viewMore');
};

// 暴露给父组件的方法
defineExpose({
    handleCourseClick,
    handleViewMore
});

// 生命周期
onMounted(() => {
    // 组件挂载后可以获取课程数据
    console.log('推荐课程组件已挂载');
});
</script>

<style lang="scss" scoped>
.recommend-courses {
    .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .more-link {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 14px;
            color: #666;
            cursor: pointer;
            transition: color 0.3s ease;

            &:hover {
                color: #ff4757;
            }

            i {
                font-size: 12px;
            }
        }
    }

    .courses-list {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .course-card {
        display: flex;
        gap: 16px;
        cursor: pointer;

        .course-cover {
            width: 80px;
            height: 90px;
            flex-shrink: 0;
            border-radius: 8px;
            overflow: hidden;

            .cover-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: transform 0.3s ease;
            }
        }

        .course-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            min-width: 0;

            .course-main {
                .course-name {
                    font-size: 15px;
                    font-weight: 600;
                    color: #212529;
                    margin: 0 0 8px 0;
                    line-height: 1.4;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .course-source {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    font-size: 13px;
                    color: #6c757d;
                    i {
                        font-size: 12px;
                    }
                }
            }

            .course-footer {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-top: 8px;

                .course-type-badge {
                    padding: 3px 10px;
                    border-radius: 12px;
                    font-size: 11px;
                    font-weight: 600;
                    color: white;

                    &.required {
                        background: linear-gradient(135deg, #ff6b6b, #ff4757);
                    }

                    &.optional {
                        background: linear-gradient(135deg, #2ed573, #1eac5e);
                    }
                }

                .course-meta {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    font-size: 12px;
                    color: #6c757d;
                }
            }
        }
    }
}
</style>
