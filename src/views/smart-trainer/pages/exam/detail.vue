<template>
    <div class="exam-detail-container">
        <!-- 顶部加载进度条 -->
        <ProgressBar :duration="400" color="linear-gradient(90deg, #0a7aff, #5856d6)" />

        <!-- 加载状态显示 -->
        <div v-if="isLoading" class="loading-container">
            <!-- <i class="pi pi-spin pi-spinner"></i> -->
        </div>

        <!-- 主要内容区域 -->
        <template v-else>
            <div class="content-wrapper">
                <!-- 头部信息区域 -->
                <div class="header-card">
                    <div class="tag-badge">
                        <i class="pi pi-flag"></i>
                        {{ taskInfo.type }}
                    </div>
                    <h1 class="title">{{ taskInfo.title }}</h1>
                    <div class="exam-stats">
                        <div class="stat-item">
                            <i class="pi pi-file-o"></i>
                            <span>共 {{ taskInfo.questionCount }}
                                {{ EXAM_TEXT.SCORE.QUESTION_SUFFIX }}</span>
                        </div>
                        <div class="stat-item">
                            <i class="pi pi-star"></i>
                            <span>{{ EXAM_TEXT.SCORE.TOTAL_SCORE }}: {{ taskInfo.criterionScore
                            }}{{ EXAM_TEXT.SCORE.SCORE_SUFFIX }}</span>
                        </div>
                    </div>
                </div>

                <!-- 能力标签区域 -->

                <div class="info-card abilities-section">
                    <h2 class="section-title">
                        {{ EXAM_TEXT.EXAM_INFO.ABILITIES }}
                    </h2>
                    <div class="ability-tags">
                        <div v-for="ability in taskInfo.abilities" :key="ability.abilityId" class="ability-tag">
                            <i class="pi pi-check-circle"></i>
                            {{ ability.abilityName }}
                        </div>
                    </div>
                </div>

                <!-- 考试描述区域 -->

                <div class="info-card description-section">
                    <h2 class="section-title">
                        {{ EXAM_TEXT.EXAM_INFO.DESCRIPTION }}
                    </h2>
                    <div class="description-box">
                        <p :class="{ expanded: isDescriptionExpanded }">
                            {{ taskInfo.description }}
                        </p>
                        <div v-if="shouldShowExpandButton" class="expand-toggle" @click="toggleDescription">
                            {{ isDescriptionExpanded ? '收起' : '展开' }}
                            <i class="pi" :class="isDescriptionExpanded ? 'pi-chevron-up' : 'pi-chevron-down'"></i>
                        </div>
                    </div>
                </div>

                <!-- 考试提示区域 -->

                <div class="info-card tips-section">
                    <h2 class="section-title">考试提示</h2>
                    <div class="tips-list">
                        <div class="tip-item" v-if="taskInfo.questionCount">
                            <i class="pi pi-clock"></i>
                            <span>{{ EXAM_TEXT.EXAM_TIPS.SUFFICIENT_TIME }}:
                                {{ taskInfo.questionCount * 5 }}分钟</span>
                        </div>
                        <div class="tip-item">
                            <i class="pi pi-mobile"></i>
                            <span>{{ EXAM_TEXT.EXAM_TIPS.STABLE_NETWORK }}</span>
                        </div>
                        <div class="tip-item">
                            <i class="pi pi-heart"></i>
                            <span>{{ EXAM_TEXT.EXAM_TIPS.GOOD_MINDSET }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部固定按钮区域 -->
            <div class="action-footer">
                <div class="button-group">
                    <Button @click="goToHome" class="btn-home">
                        <i class="pi pi-home"></i>
                        回到首页
                    </Button>
                    <Button @click="startTraining" class="btn-start">
                        <i class="pi pi-play"></i>
                        {{ EXAM_TEXT.BUTTONS.START_TRAINING }}
                    </Button>
                </div>
            </div>
        </template>

        <!-- PC端提示弹窗 -->
        <PCQrcodeDialog v-model:visible="showPCDialog" :show-back-button="true" :show-close-button="true" />

        <!-- 未完成试卷底部弹窗 -->
        <Transition name="slide-up">
            <div v-if="showUncompletedDialog" class="uncompleted-dialog">
                <div class="dialog-header">
                    <h3>您有未完成的考试</h3>
                </div>

                <div class="time-info" v-if="uncompletedPapers.length > 0">
                    <i class="pi pi-calendar"></i>
                    <span>{{ formatDate(uncompletedPapers[0]?.createdStime) }}</span>
                </div>

                <div class="dialog-actions">
                    <Button class="btn-continue" @click="handleSelectPaper(uncompletedPapers[0])">
                        <i class="pi pi-replay"></i>
                        继续练习
                    </Button>
                    <Button class="btn-restart" @click="handleStartNewExam">
                        <i class="pi pi-refresh"></i>
                        重新练习
                    </Button>
                </div>
            </div>
        </Transition>

        <!-- 遮罩层 -->
        <Transition name="fade">
            <div v-if="showUncompletedDialog" class="overlay" @click="showUncompletedDialog = false"></div>
        </Transition>
    </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { setNavigationTitle } from 'dingtalk-jsapi'; // 只导入需要的方法
import { useRouter, useRoute } from 'vue-router';
import useTrainerStore from '@/stores/trainer';
import PCQrcodeDialog from '@/components/PCQrcodeDialog.vue';
import ProgressBar from '@/components/common/ProgressBar.vue';
import { TRAINING_TEXT_SCHEMA2 as EXAM_TEXT } from '../../constants/text';
import dayjs from 'dayjs';
import { isDingTalkPC } from '@/utils/index';

const router = useRouter();
const route = useRoute();
const trainerStore = useTrainerStore();

// 状态变量
const taskInfo = ref({});
const showPCDialog = ref(false);
const isLoading = ref(true);
const isDescriptionExpanded = ref(false);
const shouldShowExpandButton = ref(false);
const showUncompletedDialog = ref(false);
const uncompletedPapers = ref([]);
const hasUncompletedPapers = ref(false); // 新增：标记是否有未完成试卷

// 设置导航标题
setNavigationTitle({
    title: EXAM_TEXT.PAGE_TITLES.TASK_DETAIL
});

/**
 * 获取考试详情
 */
async function fetchExamDetail() {
    isLoading.value = true;
    const { examId } = route.query;
    try {
        const result = await trainerStore.getExamDetail({
            examId
        });
        if (result) {
            taskInfo.value = {
                id: result.id,
                title: result.name,
                type: EXAM_TEXT.EXAM_INFO.TYPE,
                description: result.desc || '暂无描述',
                questionCount: result.questionCount,
                abilities: result.abilities || [],
                criterionScore: result.criterionScore
            };
        }
    } catch (error) {
        console.error('获取练习详情失败:', error);
    } finally {
        isLoading.value = false;
    }
}

/**
 * 静默获取未完成试卷列表
 * 不阻塞页面渲染，提前获取数据
 */
async function silentFetchUncompletedPapers() {
    const { examId } = route.query;

    // 防护性检查：确保 examId 存在且有效
    if (!examId || examId === 'undefined' || examId === 'null') {
        console.warn('examId 参数无效，跳过未完成试卷查询:', examId);
        return false;
    }

    try {
        // 调用获取未完成试卷列表的接口
        await trainerStore.getUncompletedPaperList(examId);

        // 将未完成试卷数据保存到本地变量
        uncompletedPapers.value = trainerStore.uncompletedPapers;

        // 标记是否有未完成试卷
        hasUncompletedPapers.value = uncompletedPapers.value.length > 0;

        return hasUncompletedPapers.value;
    } catch (error) {
        console.error('静默获取未完成试卷失败:', error);
        return false;
    }
}

/**
 * 跳转到首页
 */
function goToHome() {
                            router.push('/smart-trainer/practice/home');
}

/**
 * 开始训练
 */
function startTraining() {
    if (isDingTalkPC()) {
        showPCDialog.value = true;
        return;
    }

    const { examId } = route.query;

    // 如果有未完成的试卷，显示弹窗
    if (hasUncompletedPapers.value) {
        showUncompletedDialog.value = true;
    } else {
        // 如果没有未完成的试卷，直接跳转到考试页面
        router.push(`/smart-trainer/exam/exam?examId=${examId}`);
    }
}

/**
 * 处理选择未完成试卷
 */
function handleSelectPaper(paper) {
    showUncompletedDialog.value = false;
    // 跳转到考试页面，并传递 paperId 参数
    router.push(`/smart-trainer/exam/exam?examId=${route.query.examId}&paperId=${paper.id}`);
}

/**
 * 处理开始新考试
 */
function handleStartNewExam() {
    showUncompletedDialog.value = false;
    // 跳转到考试页面，并传递 retry=true 参数，表示强制创建新试卷
    router.push(`/smart-trainer/exam/exam?examId=${route.query.examId}&retry=true`);
}

/**
 * 切换描述展开/收起状态
 */
function toggleDescription() {
    isDescriptionExpanded.value = !isDescriptionExpanded.value;
}

/**
 * 检查描述内容是否需要展开按钮
 */
function checkDescriptionLength() {
    // 使用 nextTick 确保 DOM 已更新
    nextTick(() => {
        const descElement = document.querySelector('.description-box p');
        if (descElement) {
            // 如果内容高度超过两行，显示展开按钮
            const lineHeight = parseInt(getComputedStyle(descElement).lineHeight);
            shouldShowExpandButton.value = descElement.scrollHeight > lineHeight * 2 + 4; // 添加一点余量
        }
    });
}

/**
 * 格式化日期
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期
 */
function formatDate(dateString) {
    if (!dateString) {
        return '未知时间';
    }

    try {
        return dayjs(dateString).format('YYYY年MM月DD日 HH:mm:ss');
    } catch (error) {
        console.error('日期格式化错误:', error);
        return '未知时间';
    }
}

onMounted(async () => {
    // 等待获取考试详情完成，然后检查描述长度
    await fetchExamDetail();
    checkDescriptionLength();

    // 静默获取未完成试卷数据（不阻塞主要逻辑）
    silentFetchUncompletedPapers();
});
</script>

<style lang="scss" scoped>
/* 隐藏滚动条 */
::-webkit-scrollbar {
    display: none;
}

/* 主容器 */
.exam-detail-container {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #f5f7fa;
}

/* 内容区域 */
.content-wrapper {
    flex: 1;
    overflow-y: auto;
    padding: 16px 16px 0;
    padding-bottom: calc(80px + env(safe-area-inset-bottom));
}

/* 加载状态 */
// .loading-container {
//     position: absolute;
//     top: 50%;
//     left: 50%;
//     transform: translate(-50%, -50%);
//     z-index: 10;

//     i {
//         font-size: 28px;
//         color: $system-blue;
//         filter: drop-shadow(0 0 4px rgba($system-background-primary, 0.9));
//         animation: spin 1.2s linear infinite;
//     }
// }

/* 头部卡片 */
.header-card {
    background: linear-gradient(135deg, $system-blue 0%, $system-indigo 100%);
    border-radius: 16px;
    padding: 24px;
    color: white;
    margin-bottom: 16px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba($system-blue, 0.2);

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/detail/detail-bg.png') center/cover;
        opacity: 0.15;
    }

    .tag-badge {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        background: rgba($system-background-primary, 0.25);
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
        margin-bottom: 12px;
        position: relative;
        backdrop-filter: blur(4px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba($system-background-primary, 0.3);
    }

    .title {
        font-size: 20px;
        font-weight: 600;
        margin: 0 0 16px 0;
        position: relative;
        line-height: 1.3;
    }

    .exam-stats {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        position: relative;

        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.2);
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 13px;
            backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
    }
}

/* 通用卡片样式 */
.info-card {
    background: white;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

    .section-title {
        font-size: 16px;
        color: #333;
        margin: 0 0 16px 0;
        font-weight: 600;
        display: flex;
        align-items: center;

        &::before {
            content: '';
            width: 4px;
            height: 16px;
            background: linear-gradient(to bottom, #0a7aff, #5856d6);
            border-radius: 2px;
            margin-right: 8px;
        }
    }
}

/* 能力标签区域 */
.abilities-section {
    .ability-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .ability-tag {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        background: rgba($system-blue, 0.08);
        color: $system-blue;
        padding: 6px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        border: 1px solid rgba($system-blue, 0.15);
    }
}

/* 描述区域 */
.description-section {
    .description-box {
        background: $system-background-secondary;
        padding: 16px;
        border-radius: 12px;

        p {
            margin: 0;
            font-size: 14px;
            line-height: 1.6;
            color: $label-secondary;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            transition: all 0.3s ease;

            &.expanded {
                -webkit-line-clamp: unset;
            }
        }

        .expand-toggle {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            margin-top: 12px;
            color: $system-blue;
            font-size: 13px;
            font-weight: 500;
        }
    }
}

/* 提示区域 */
.tips-section {
    .tips-list {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .tip-item {
        display: flex;
        align-items: center;
        gap: 12px;
        background: $system-background-secondary;
        padding: 14px;
        border-radius: 12px;

        i {
            color: $system-teal;
            font-size: 16px;
        }

        span {
            font-size: 14px;
            color: $label-secondary;
        }
    }
}

/* 底部按钮区域 */
.action-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba($system-background-primary, 0.95);
    backdrop-filter: blur(10px);
    padding: 16px 16px 10px;
    box-shadow: 0 -4px 16px rgba($label-primary, 0.06);
    z-index: 100;

    .button-group {
        display: flex;
        gap: 12px;
        max-width: 400px;
        margin: 0 auto;
    }

    .btn-home,
    .btn-start {
        flex: 1;
        height: 48px;
        border-radius: 24px;
        font-size: 15px;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .btn-home {
        background: $system-background-primary;
        color: $label-primary;
        border: 1px solid $system-gray4;

        i {
            color: $label-secondary;
        }
    }

    .btn-start {
        background: linear-gradient(135deg, $system-blue, $system-indigo);
        color: $system-background-primary;
        border: none;
    }
}

/* 未完成试卷弹窗 */
.uncompleted-dialog {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: $system-background-primary;
    border-radius: 24px 24px 0 0;
    padding: 24px 20px;
    z-index: 1001;

    .dialog-header {
        text-align: center;
        margin-bottom: 16px;

        h3 {
            font-size: 18px;
            font-weight: 600;
            color: $label-primary;
            margin: 0;
        }
    }

    .time-info {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        margin-bottom: 20px;

        i {
            color: $system-teal;
        }

        span {
            font-size: 14px;
            color: $label-secondary;
        }
    }

    .dialog-actions {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .btn-continue,
        .btn-restart {
            height: 48px;
            border-radius: 24px;
            font-size: 15px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-continue {
            background: linear-gradient(135deg, $system-blue, $system-indigo);
            color: $system-background-primary;
            border: none;
        }

        .btn-restart {
            background: linear-gradient(135deg, $system-teal, $system-green);
            color: $system-background-primary;
            border: none;
        }
    }
}

/* 遮罩层 */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba($label-primary, 0.5);
    backdrop-filter: blur(4px);
    z-index: 1000;
}

/* 动画 */
.slide-up-enter-active,
.slide-up-leave-active {
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
    transform: translateY(100%);
    opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}
</style>
