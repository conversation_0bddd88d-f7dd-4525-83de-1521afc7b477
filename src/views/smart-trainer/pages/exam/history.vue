<template>
    <section class="review-detail">
        <LoadingState v-if="isLoading" :text="EXAM_TEXT.STATUS.LOADING_EXAM" />

        <!-- 顶部概览区域 -->
        <div class="review-header">
            <div class="review-info">
                <div class="task-info">
                    <div class="task-title">《{{ route.query.paperName || '-' }}》</div>
                </div>
                <div class="stats">
                    <div class="stat-item">
                        <span class="label mr-2">{{ EXAM_TEXT.SCORE.TOTAL_SCORE }}</span>
                        <span class="value"
                            >{{ route.query.paperScore || 0
                            }}{{ EXAM_TEXT.SCORE.SCORE_SUFFIX }}</span
                        >
                    </div>
                    <div class="stat-item">
                        <span class="label mr-2">{{ EXAM_TEXT.SCORE.QUESTION_COUNT }}</span>
                        <span class="value"
                            >{{ messages.length }}{{ EXAM_TEXT.SCORE.QUESTION_SUFFIX }}</span
                        >
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="review-content">
            <div class="timeline">
                <!-- 题目时间轴 -->
                <div v-for="(question, index) in messages" :key="question.id" class="timeline-item">
                    <div class="timeline-index">Q{{ index + 1 }}</div>
                    <div class="timeline-content">
                        <!-- AI提问卡片 -->
                        <div class="question-card">
                            <div class="card-header">
                                <div class="avatar">
                                    <img
                                        src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/woman.png"
                                        alt="AI头像"
                                    />
                                </div>
                                <span class="title">AI提问</span>
                            </div>
                            <div class="card-content">
                                <AudioPlayer
                                    class="question-audio-wrapper"
                                    v-if="question.questionTextTtsUrl"
                                    :audio-url="getFullAudioUrl(question.questionTextTtsUrl)"
                                    :text="question.questionText"
                                />
                            </div>
                        </div>

                        <!-- 用户回答卡片 -->
                        <template v-if="question.paperAnswers && question.paperAnswers.length">
                            <div
                                v-for="answer in question.paperAnswers"
                                :key="answer.id"
                                class="answer-card"
                            >
                                <div class="card-header">
                                    <div class="avatar">
                                        <img
                                            src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/default-avatar.png"
                                            alt="用户头像"
                                        />
                                    </div>
                                    <span class="title">我的回答</span>
                                </div>
                                <div class="card-content">
                                    <DingAudioPlayer
                                        class="answer-audio-wrapper"
                                        v-if="answer.dingMediaId"
                                        theme="green"
                                        :audio-id="answer.dingMediaId"
                                        :audio-duration="answer.duration"
                                        :audio-text="answer.answerAudioAsrRepaired"
                                    />
                                </div>
                            </div>
                        </template>

                        <!-- 评价卡片 -->
                        <div
                            v-if="question.paperCriterionAssessments?.length"
                            class="assessment-card"
                        >
                            <Assessment :assessments="question.paperCriterionAssessments" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部返回按钮 -->
        <div class="review-footer">
            <Button
                :label="EXAM_TEXT.BUTTONS.BACK"
                icon="pi pi-arrow-left"
                @click="goBack"
                severity="primary"
            />
        </div>
    </section>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import * as dd from 'dingtalk-jsapi'; // 直接导入
import { useRouter, useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import useChatStore from '@/stores/chat';
import useTrainerStore from '@/stores/trainer';
import { getFullAudioUrl } from '@/utils/trainer';
import AudioPlayer from '@/components/AudioPlayer/index.vue';
import DingAudioPlayer from '@/components/DingAudioPlayer/index.vue';
import Assessment from '@/views/smart-trainer/components/Assessment.vue';
import LoadingState from '@/components/common/LoadingState.vue';
import { TRAINING_TEXT_SCHEMA2 as EXAM_TEXT } from '../../constants/text';
import audioManager from '@/utils/audioManager';
import { isDingTalk } from '@/utils/index';

// 路由相关
const route = useRoute();
const router = useRouter();

// Store
const chatStore = useChatStore();
const trainerStore = useTrainerStore();
const { messages } = storeToRefs(chatStore);

// 加载状态
const isLoading = ref(false);

// 设置导航栏标题
if (isDingTalk()) {
    dd.setNavigationTitle({
        title: EXAM_TEXT.PAGE_TITLES.EXAM_REVIEW
    });
}

/**
 * 获取历史消息列表
 * @param {string} paperId - 试卷ID
 */
async function getMessageList(paperId) {
    try {
        isLoading.value = true;

        const result = await trainerStore.getHistoryMessageList({
            paperId,
            pageIndex: 1,
            pageSize: 1000
        });

        if (result && result.list && result.list.length > 0) {
            messages.value = result.list.reverse();
            return true;
        }
        return false;
    } catch (error) {
        console.error('获取历史消息失败:', error.message);
        return false;
    } finally {
        isLoading.value = false;
    }
}

/**
 * 返回上一页
 */
const goBack = () => {
    router.back();
};

/**
 * 初始化函数
 */
const init = async () => {
    const { paperId } = route.query;
    if (!paperId) {
                        router.replace('/smart-trainer/practice');
        return;
    }

    await getMessageList(paperId);
};

// 组件卸载时清理
onMounted(() => {
    // 清空之前的消息
    chatStore.clearMessages();
    // 初始化
    init();
});

onBeforeUnmount(() => {
    // 停止所有正在播放的音频
    audioManager.stopAll();
});
</script>

<style lang="scss" scoped>
.review-detail {
    height: 100%;
    background: #f8faff;
    display: flex;
    flex-direction: column;
    display: flex;
    flex-direction: column;

    .review-header {
        background: linear-gradient(135deg, #1677ff 0%, #4096ff 100%);
        padding: 20px 16px;
        color: #fff;

        .review-info {
            .task-info {
                margin-bottom: 12px;

                .task-title {
                    font-size: 18px;
                    font-weight: 600;
                    margin-bottom: 10px;
                }

                .time {
                    font-size: 14px;
                    opacity: 0.8;
                }
            }

            .stats {
                display: flex;
                gap: 24px;

                .stat-item {
                    .label {
                        font-size: 13px;
                        opacity: 0.8;
                        margin-bottom: 4px;
                    }
                    .value {
                        font-size: 20px;
                        font-weight: 600;
                    }
                }
            }
        }
    }

    .review-content {
        flex: 1;
        padding: 20px 16px;
        overflow-y: auto;

        .timeline {
            position: relative;

            &::before {
                content: '';
                position: absolute;
                left: 15px;
                top: 0;
                bottom: 0;
                width: 2px;
                background: linear-gradient(180deg, #4096ff 0%, rgba(64, 150, 255, 0.3) 100%);
                box-shadow: 0 0 8px rgba(64, 150, 255, 0.3);
            }

            .timeline-item {
                position: relative;
                padding-left: 40px;
                margin-bottom: 32px;

                .timeline-index {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 32px;
                    height: 32px;
                    background: linear-gradient(135deg, #4096ff 0%, #1677ff 100%);
                    border: 2px solid #ffffff;
                    box-shadow: 0 0 12px rgba(64, 150, 255, 0.4);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 12px;
                    font-weight: 600;
                    color: #ffffff;
                    transition: all 0.3s ease;

                    &::after {
                        content: '';
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                        border: 2px solid #4096ff;
                        animation: pulse 2s infinite;
                    }
                }

                .timeline-content {
                    .question-card,
                    .answer-card,
                    .assessment-card {
                        background: #fff;
                        border-radius: 12px;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
                        margin-bottom: 16px;
                        overflow: hidden;
                        animation: slideIn 0.3s ease;

                        .card-header {
                            padding: 12px 16px;
                            display: flex;
                            align-items: center;
                            gap: 8px;
                            border-bottom: 1px solid rgba(0, 0, 0, 0.06);

                            .avatar {
                                width: 24px;
                                height: 24px;
                                border-radius: 50%;
                                overflow: hidden;
                                img {
                                    width: 100%;
                                    height: 100%;
                                    object-fit: cover;
                                }
                            }

                            .title {
                                font-size: 14px;
                                font-weight: 500;
                                color: #333;
                            }

                            i {
                                font-size: 16px;
                                color: #1677ff;
                            }
                        }

                        .card-content {
                            padding: 16px;

                            .question-audio-wrapper,
                            .answer-audio-wrapper {
                                width: 100%;
                            }
                        }
                    }

                    .assessment-card {
                        background: #f8faff;
                    }
                }
            }
        }
    }

    .review-footer {
        flex-shrink: 0;
        padding: 16px;
        padding-bottom: calc(48px + env(safe-area-inset-bottom));
        background: linear-gradient(180deg, rgba(248, 250, 255, 0) 0%, #f8faff 100%);
        display: flex;
        justify-content: center;

        :deep(.p-button) {
            min-width: 120px;
            border-radius: 20px;
            transition: all 0.3s ease;

            &:active {
                transform: scale(0.98);
            }
        }
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.8;
    }
    70% {
        transform: scale(1.3);
        opacity: 0;
    }
    100% {
        transform: scale(1.3);
        opacity: 0;
    }
}
</style>
