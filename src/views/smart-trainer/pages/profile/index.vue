<template>
    <div class="profile-container">
        <!-- 用户信息头部 -->
        <div class="profile-header">
            <div class="user-info">
                <div class="avatar-container">
                    <img :src="userInfo.avatar" :alt="userInfo.name" class="user-avatar" />
                    <div class="avatar-badge">
                        <i class="pi pi-check"></i>
                    </div>
                </div>
                <div class="user-details">
                    <h2 class="user-name">{{ userInfo.name }}</h2>
                    <p class="user-title">{{ userInfo.title }}</p>
                    <div class="user-stats">
                        <span class="stat-item">学习天数: {{ userInfo.studyDays }}</span>
                        <span class="stat-divider">|</span>
                        <span class="stat-item">积分: {{ userInfo.points }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学习统计 -->
        <div class="stats-section">
            <h3 class="section-title">学习统计</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon completed">
                        <i class="pi pi-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ learningStats.completed }}</div>
                        <div class="stat-label">已完成课程</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon in-progress">
                        <i class="pi pi-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ learningStats.inProgress }}</div>
                        <div class="stat-label">进行中</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon practice">
                        <i class="pi pi-play-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ learningStats.practiceCount }}</div>
                        <div class="stat-label">练习次数</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon exam">
                        <i class="pi pi-file-edit"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ learningStats.examCount }}</div>
                        <div class="stat-label">考试次数</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能菜单 -->
        <div class="menu-section">
            <h3 class="section-title">功能菜单</h3>
            <div class="menu-list">
                <div 
                    v-for="item in menuItems" 
                    :key="item.key"
                    class="menu-item"
                    @click="handleMenuClick(item)"
                >
                    <div class="menu-icon">
                        <i :class="item.icon"></i>
                    </div>
                    <div class="menu-content">
                        <div class="menu-title">{{ item.title }}</div>
                        <div class="menu-subtitle">{{ item.subtitle }}</div>
                    </div>
                    <div class="menu-arrow">
                        <i class="pi pi-chevron-right"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 用户信息
const userInfo = ref({
    name: '张三',
    title: '高级学员',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
    studyDays: 45,
    points: 1280
});

// 学习统计
const learningStats = ref({
    completed: 12,
    inProgress: 3,
    practiceCount: 28,
    examCount: 8
});

// 功能菜单
const menuItems = ref([
    {
        key: 'learning-record',
        title: '学习记录',
        subtitle: '查看学习历史和进度',
        icon: 'pi pi-history'
    },
    {
        key: 'achievements',
        title: '成就徽章',
        subtitle: '查看获得的成就和徽章',
        icon: 'pi pi-star'
    },
    {
        key: 'settings',
        title: '设置',
        subtitle: '个人设置和偏好配置',
        icon: 'pi pi-cog'
    },
    {
        key: 'feedback',
        title: '意见反馈',
        subtitle: '提交建议和问题反馈',
        icon: 'pi pi-comment'
    },
    {
        key: 'about',
        title: '关于我们',
        subtitle: '了解更多产品信息',
        icon: 'pi pi-info-circle'
    }
]);

/**
 * 处理菜单点击
 */
const handleMenuClick = (item) => {
    console.log(`点击菜单: ${item.title}`);
    // 这里可以根据不同的菜单项进行路由跳转或其他操作
    switch (item.key) {
        case 'learning-record':
            // router.push('/smart-trainer/profile/learning-record');
            break;
        case 'achievements':
            // router.push('/smart-trainer/profile/achievements');
            break;
        case 'settings':
            // router.push('/smart-trainer/profile/settings');
            break;
        case 'feedback':
            // 打开反馈页面或弹窗
            break;
        case 'about':
            // 打开关于页面
            break;
    }
};

onMounted(() => {
    console.log('个人中心页面已加载');
    // 这里可以加载用户数据和学习统计
});
</script>

<style lang="scss" scoped>
.profile-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    
    .profile-header {
        padding: 20px 16px 30px;
        color: white;
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 16px;
            
            .avatar-container {
                position: relative;
                
                .user-avatar {
                    width: 80px;
                    height: 80px;
                    border-radius: 50%;
                    border: 3px solid rgba(255, 255, 255, 0.3);
                    object-fit: cover;
                }
                
                .avatar-badge {
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    width: 24px;
                    height: 24px;
                    background: #10b981;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border: 2px solid white;
                    
                    i {
                        font-size: 12px;
                        color: white;
                    }
                }
            }
            
            .user-details {
                flex: 1;
                
                .user-name {
                    font-size: 24px;
                    font-weight: 600;
                    margin-bottom: 4px;
                }
                
                .user-title {
                    font-size: 14px;
                    opacity: 0.9;
                    margin-bottom: 8px;
                }
                
                .user-stats {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-size: 12px;
                    opacity: 0.8;
                    
                    .stat-divider {
                        opacity: 0.6;
                    }
                }
            }
        }
    }
    
    .stats-section,
    .menu-section {
        background: white;
        margin: 0 16px 16px;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
        }
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        
        .stat-card {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
            
            .stat-icon {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                
                i {
                    font-size: 18px;
                    color: white;
                }
                
                &.completed {
                    background: #10b981;
                }
                
                &.in-progress {
                    background: #f59e0b;
                }
                
                &.practice {
                    background: #3b82f6;
                }
                
                &.exam {
                    background: #8b5cf6;
                }
            }
            
            .stat-content {
                .stat-number {
                    font-size: 20px;
                    font-weight: 600;
                    color: #1f2937;
                    line-height: 1;
                }
                
                .stat-label {
                    font-size: 12px;
                    color: #6b7280;
                    margin-top: 2px;
                }
            }
        }
    }
    
    .menu-list {
        .menu-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 0;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: background-color 0.2s ease;
            
            &:last-child {
                border-bottom: none;
            }
            
            &:hover {
                background-color: #f9fafb;
                margin: 0 -20px;
                padding-left: 20px;
                padding-right: 20px;
            }
            
            .menu-icon {
                width: 40px;
                height: 40px;
                background: #f3f4f6;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                
                i {
                    font-size: 18px;
                    color: #6b7280;
                }
            }
            
            .menu-content {
                flex: 1;
                
                .menu-title {
                    font-size: 16px;
                    font-weight: 500;
                    color: #1f2937;
                    margin-bottom: 2px;
                }
                
                .menu-subtitle {
                    font-size: 12px;
                    color: #6b7280;
                }
            }
            
            .menu-arrow {
                i {
                    font-size: 14px;
                    color: #9ca3af;
                }
            }
        }
    }
}
</style>
