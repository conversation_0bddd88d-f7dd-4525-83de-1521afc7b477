<template>
    <div class="assess-home">
        <div class="content">
            <h2>考核首页</h2>
            <p>这里将是考核模块的主要内容</p>
            <div class="placeholder">
                <i class="pi pi-chart-line text-4xl text-red-500"></i>
                <p>考核功能开发中...</p>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue';

onMounted(() => {
    console.log('考核首页已加载');
});
</script>

<style lang="scss" scoped>
.assess-home {
    padding: 20px 16px;
    height: 100%;
}

.content {
    text-align: center;
    padding-top: 60px;

    h2 {
        font-size: 20px;
        margin-bottom: 12px;
        color: #333;
    }

    p {
        color: #666;
        margin-bottom: 40px;
    }
}

.placeholder {
    background: white;
    border-radius: 12px;
    padding: 60px 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    i {
        margin-bottom: 20px;
    }

    p {
        color: #999;
        font-size: 14px;
    }
}
</style>
