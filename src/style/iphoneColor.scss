// 用户颜色

$user-color-1: rgba(255, 149, 0, 1);
$user-accent-color: rgba(10, 122, 255, 1);

// 系统颜色

$system-red: rgba(255, 59, 48, 1);
$system-orange: rgba(255, 149, 0, 1);
$system-yellow: rgba(255, 204, 0, 1);
$system-green: rgba(52, 199, 89, 1);
$system-teal: rgba(90, 200, 250, 1);
$system-blue: rgba(10, 122, 255, 1);
$system-indigo: rgba(88, 86, 214, 1);
$system-purple: rgba(175, 82, 222, 1);
$system-pink: rgba(255, 45, 85, 1);
$system-gray: rgba(142, 142, 147, 1);
$system-gray2: rgba(174, 174, 178, 1);
$system-gray3: rgba(199, 199, 204, 1);
$system-gray4: rgba(209, 209, 214, 1);
$system-gray5: rgba(229, 229, 234, 1);
$system-gray6: rgba(242, 242, 247, 1);
$system-gray7: rgba(249, 249, 250, 1);

// 文字颜色
$label-primary: rgba(0, 0, 0, 1);
$label-secondary: rgba(60, 60, 67, 0.6);
$label-tertiary: rgba(60, 60, 67, 0.3);
$label-quaternary: rgba(60, 60, 67, 0.18);

// 系统背景色
$system-background-primary: rgba(255, 255, 255, 1);
$system-background-secondary: rgba(242, 242, 247, 1);
$system-background-tertiary: rgba(255, 255, 255, 1);

// 系统分组背景色
$system-grouped-background-primary: rgba(242, 242, 247, 1);
$system-grouped-background-secondary: rgba(255, 255, 255, 1);
$system-grouped-background-tertiary: rgba(242, 242, 247, 1);

// 填充颜色
$fill-color-primary: rgba(120, 120, 128, 0.2);
$fill-color-secondary: rgba(120, 120, 128, 0.16);
$fill-color-tertiary: rgba(118, 118, 128, 0.12);
$fill-color-quaternary: rgba(116, 116, 128, 0.08);

// 系统Material
$system-material-thick: rgba(250, 250, 250, 0.93);
$system-material-regular: rgba(237, 237, 237, 0.8);
$system-material-thin: rgba(227, 227, 227, 0.65);
$system-material-ultrathin: rgba(227, 227, 227, 0.42);

// 分割线颜色
$separator-color-opaque: rgba(198, 198, 200, 1);
$separator-color-non-opaque: rgba(60, 60, 67, 0.36);
