@import '@/primeFlex.css';
@import '@/normalize.css';
@import '@/style/markdown.scss';

:root {
    font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    font-weight: 400;

    color-scheme: light dark;
    color: rgba(255, 255, 255, 0.87);

    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
}

html,
body {
    width: 100%;
    height: 100%;
    overflow: hidden;
    /* 禁止滑动 */
    margin: 0;
    padding: 0;
    background-color: #fff;
}

body {
    color: #333;
    -webkit-overflow-scrolling: touch;
}

// 全局禁止图片长按保存
img {
    -webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none; /* Safari */
    -khtml-user-select: none; /* Konqueror HTML */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
    user-select: none; /* Non-prefixed version */
    pointer-events: none; /* 禁止任何指针事件 */
}

#app {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    overflow: hidden;
    /* 确保内部元素不超出视口 */
}

h3 {
    margin: 0;
    padding: 0;
}

p {
    margin: 0;
    padding: 0;
}

a {
    text-decoration: none;
}

a:hover,
a:focus,
a:active {
    text-decoration: none;
}

.p-scrollpanel-bar-y {
    width: 5px;
}

.p-toast {
    top: 10px !important;
    right: 0;
    left: 50%;
    transform: translateX(-50%);
}

div {
    box-sizing: border-box;
}

.p-toast-icon-close {
    outline: none !important;
    right: 0 !important;
}

.p-confirm-dialog {
    width: 70%;
}

.p-toast-message-content {
    .p-toast-message-icon {
        width: 20px;
        height: 20px;
    }

    .p-toast-icon-close-icon {
        width: 20px;
        height: 20px;
    }

    .p-toast-summary {
        font-size: 18px;
        font-weight: bold;
    }

    .p-toast-detail {
        font-size: 14px;
        font-weight: bold;
    }
}

* {
    // 点击高亮
    -webkit-tap-highlight-color: transparent;
    outline: none;
    touch-action: manipulation;
}

/* PC端滚动条样式优化 */
::-webkit-scrollbar {
    width: 4px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

/* Firefox滚动条样式 */
* {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

/* PrimeVue Calendar 组件 z-index 修正 */
.p-datepicker {
    z-index: 10000 !important;
}

.p-datepicker-panel {
    z-index: 10000 !important;
}

/* 确保在模态框中的 Calendar 组件能正常显示 */
.modal-selector .p-datepicker,
.modal-selector .p-datepicker-panel {
    z-index: 10001 !important;
}
